#!/usr/bin/env python3
"""
DMS端到端一体化分析流程
从视频裁剪到最终渲染的完整自动化程序

使用方法:
    python dms_pipeline.py -i video.mp4 -t "00:01:00-00:01:30" [选项]
"""

import os
import sys
import json
import argparse
import time
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

# 添加scripts目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
from rich.table import Table
from rich.prompt import Confirm

from asset_detector import AssetDetector
from core_modules import Config, InputValidator, ResourceMonitor, ProgressDisplay, FFmpegProcessor
from asset_package_manager import AssetPackageManager
from cpp_kit_manager import CppKitManager
from config_validator import ConfigValidator


class DMSPipeline:
    """DMS端到端一体化分析流程"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化流程管理器
        
        Args:
            config_path: 配置文件路径，默认使用pipeline_config.json
        """
        self.console = Console()
        self.start_time = datetime.now()
        
        # 加载配置
        if config_path is None:
            config_path = Path(__file__).parent / "pipeline_config.json"
        
        self.config = self._load_config(config_path)
        self.setup_directories()
        
        # 初始化组件
        self.asset_detector = AssetDetector(
            self.config,
            Path(self.config["paths"]["assets_dir"])
        )
        self.asset_manager = AssetPackageManager(self.config["paths"]["assets_dir"])

        # 初始化视频处理组件
        self.video_config = Config()  # 使用默认配置
        self.ffmpeg_processor = FFmpegProcessor(self.video_config)

        # 初始化C++处理组件
        try:
            from cpp_kit_manager import CppKitManager
            from config_validator import ConfigValidator
            self.cpp_kit_manager = CppKitManager(self.config["paths"]["cpp_kits_dir"])
            self.config_validator = ConfigValidator()
        except ImportError as e:
            self.console.print(f"⚠️ C++处理模块导入失败: {e}", style="yellow")
            self.cpp_kit_manager = None
            self.config_validator = None
        
        # 流程状态
        self.current_stage = ""
        self.asset_info = {}
        self.processing_results = {}
        
    def _load_config(self, config_path: Path) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            self.console.print(f"✅ 配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            self.console.print(f"❌ 配置文件加载失败: {e}", style="red")
            sys.exit(1)
    
    def setup_directories(self):
        """创建必要的目录"""
        paths = self.config["paths"]
        for key, path in paths.items():
            dir_path = Path(path)
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def print_banner(self):
        """打印程序横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                    DMS端到端一体化分析流程                      ║
║                                                              ║
║  🎬 视频裁剪 → 🔧 C++分析 → 🎥 视频渲染                        ║
║                                                              ║
║  从原始视频到最终可视化分析视频的完整自动化流程                    ║
╚══════════════════════════════════════════════════════════════╝
        """
        self.console.print(Panel(banner, style="cyan"))
    
    def validate_input(self, video_path: str, time_range: str) -> Tuple[bool, str]:
        """
        验证输入参数
        
        Args:
            video_path: 视频文件路径
            time_range: 时间范围
            
        Returns:
            (is_valid, message) 元组
        """
        # 检查视频文件是否存在
        if not Path(video_path).exists():
            return False, f"视频文件不存在: {video_path}"
        
        # 检查视频格式
        supported_formats = self.config["input_settings"]["supported_video_formats"]
        video_ext = Path(video_path).suffix.lower()
        if video_ext not in supported_formats:
            return False, f"不支持的视频格式: {video_ext}，支持的格式: {supported_formats}"
        
        # 验证时间范围格式
        try:
            self.asset_detector.parse_time_range(time_range)
        except ValueError as e:
            return False, str(e)
        
        return True, "输入验证通过"
    
    def detect_existing_asset(self, video_path: str, time_range: str) -> Dict[str, Any]:
        """检测已存在的资产包"""
        self.console.print("\n🔍 检测已存在的资产包...")
        
        detection_result = self.asset_detector.detect_asset(video_path, time_range)
        
        # 显示检测结果
        table = Table(title="资产包检测结果")
        table.add_column("项目", style="cyan")
        table.add_column("值", style="white")
        
        table.add_row("资产包名称", detection_result["asset_name"])
        table.add_row("是否存在", "✅ 是" if detection_result["exists"] else "❌ 否")
        table.add_row("可以跳过", "✅ 是" if detection_result["can_skip"] else "❌ 否")
        table.add_row("状态信息", detection_result["message"])
        
        self.console.print(table)
        
        return detection_result
    
    def stage1_video_extraction(self, video_path: str, time_range: str,
                               asset_name: str, force_recreate: bool = False) -> bool:
        """
        阶段一：视频裁剪和帧提取

        Args:
            video_path: 视频文件路径
            time_range: 时间范围
            asset_name: 资产包名称
            force_recreate: 是否强制重新创建

        Returns:
            是否成功
        """
        self.current_stage = "视频裁剪和帧提取"
        self.console.print(f"\n🎬 开始阶段一: {self.current_stage}")

        # 检查是否可以跳过
        if not force_recreate:
            detection_result = self.detect_existing_asset(video_path, time_range)
            if detection_result["can_skip"]:
                self.console.print("✅ 检测到有效的资产包，跳过视频提取阶段", style="green")
                self.asset_info = detection_result
                return True

        try:
            # 解析时间范围
            start_time, end_time = self.asset_detector.parse_time_range(time_range)

            # 获取视频信息
            video_info = self.ffmpeg_processor.get_video_info(video_path)

            # 创建资产包
            source_info = {
                "video_path": os.path.abspath(video_path),
                "video_size": {
                    "width": video_info["width"],
                    "height": video_info["height"]
                },
                "video_fps": video_info.get("fps", 30.0),
                "video_duration": video_info.get("duration", 0.0),
                "video_codec": video_info.get("codec", "unknown")
            }

            extraction_info = {
                "time_range": {
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": self._time_to_seconds(end_time) - self._time_to_seconds(start_time)
                },
                "target_fps": self.config["extraction_settings"].get("preserve_original_fps", True) and video_info.get("fps", 30.0) or 10.0,
                "output_format": self.config["extraction_settings"]["output_format"],
                "frame_size": {
                    "width": video_info["width"],
                    "height": video_info["height"]
                }
            }

            # 创建资产包
            asset_path = self.asset_manager.create_asset_package(
                asset_name, source_info, extraction_info
            )
            frames_dir = asset_path / "frames"

            # 提取帧
            success = self._extract_frames_with_ffmpeg(video_path, start_time, end_time, frames_dir, extraction_info)

            if success:
                # 更新帧信息
                frame_files = sorted([f.name for f in frames_dir.glob("*.png")])
                self.asset_manager.update_frame_info(asset_name, frame_files)

                self.console.print(f"✅ 阶段一完成，提取了 {len(frame_files)} 帧", style="green")
                self.asset_info = {"asset_name": asset_name, "asset_path": str(asset_path)}
                return True
            else:
                self.console.print("❌ 帧提取失败", style="red")
                return False

        except Exception as e:
            self.console.print(f"❌ 视频提取失败: {e}", style="red")
            return False

    def _time_to_seconds(self, time_str: str) -> float:
        """时间字符串转秒数"""
        try:
            parts = time_str.split(':')
            if len(parts) == 3:
                h, m, s = map(float, parts)
                return h * 3600 + m * 60 + s
        except:
            pass
        return 0.0

    def _extract_frames_with_ffmpeg(self, video_path: str, start_time: str, end_time: str,
                                   output_dir: Path, extraction_info: Dict[str, Any]) -> bool:
        """使用FFmpeg提取帧序列"""
        try:
            import subprocess

            # 构建FFmpeg命令
            target_fps = extraction_info.get('target_fps', 10.0)
            output_format = extraction_info.get('output_format', 'png')

            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', start_time,
                '-to', end_time,
                '-vf', f'fps={target_fps}',
                '-q:v', '1',  # 高质量
                str(output_dir / f"%06d.{output_format}")
            ]

            self.console.print(f"[blue]执行FFmpeg命令: {' '.join(cmd)}[/blue]")

            # 启动FFmpeg进程
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("正在提取视频帧...", total=100)

                process = subprocess.Popen(
                    cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                    text=True, bufsize=1, universal_newlines=True
                )

                total_duration = self._time_to_seconds(end_time) - self._time_to_seconds(start_time)

                while True:
                    # 检查进程是否结束
                    if process.poll() is not None:
                        break

                    # 读取stderr输出（FFmpeg的进度信息）
                    try:
                        line = process.stderr.readline()
                        if line:
                            # 解析进度信息
                            progress_percent = self._parse_ffmpeg_progress(line, total_duration)
                            if progress_percent is not None:
                                progress.update(task, completed=progress_percent)
                    except:
                        pass

                    time.sleep(0.1)

                # 等待进程完成
                return_code = process.wait(timeout=30)
                progress.update(task, completed=100)
                return return_code == 0

        except Exception as e:
            self.console.print(f"[red]FFmpeg执行异常: {e}[/red]")
            return False

    def _parse_ffmpeg_progress(self, line: str, total_duration: float) -> Optional[float]:
        """解析FFmpeg进度信息"""
        try:
            if 'time=' in line:
                # 提取时间信息，格式如: time=00:01:23.45
                time_part = line.split('time=')[1].split()[0]
                current_seconds = self._time_to_seconds(time_part)
                if total_duration > 0:
                    return min((current_seconds / total_duration) * 100, 100)
        except:
            pass
        return None

    def stage2_cpp_processing(self, asset_name: str) -> bool:
        """
        阶段二：C++算法分析

        Args:
            asset_name: 资产包名称

        Returns:
            是否成功
        """
        self.current_stage = "C++算法分析"
        self.console.print(f"\n🔧 开始阶段二: {self.current_stage}")

        # 检查C++组件是否可用
        if not self.cpp_kit_manager:
            self.console.print("❌ C++处理组件不可用", style="red")
            return False

        try:
            # 获取可用的C++工具包
            available_kits = self.cpp_kit_manager.list_available_kits()
            if not available_kits:
                self.console.print("❌ 没有可用的C++工具包", style="red")
                return False

            # 选择第一个可用的工具包（后续可以改为用户选择）
            kit_name = available_kits[0]
            self.console.print(f"📦 使用C++工具包: {kit_name}")

            # 获取工具包信息
            kit_info = self.cpp_kit_manager.get_kit_info(kit_name)
            if not kit_info:
                self.console.print(f"❌ 无法获取工具包信息: {kit_name}", style="red")
                return False

            # 检查是否需要配置确认
            cpp_config = self.config["cpp_processing"]
            if cpp_config.get("config_validation", True):
                if not Confirm.ask("是否确认开始C++处理？"):
                    self.console.print("❌ 用户取消C++处理", style="yellow")
                    return False

            # 加载资产包
            metadata, frame_files = self.asset_manager.load_asset_package(asset_name)
            if not frame_files:
                self.console.print(f"❌ 资产包中没有帧文件: {asset_name}", style="red")
                return False

            # 创建结果目录
            result_dir = Path(self.config["paths"]["results_dir"]) / f"{asset_name}_{kit_name}_{int(time.time())}"
            result_dir.mkdir(parents=True, exist_ok=True)

            # 预处理图像（裁剪和缩放）
            self.console.print("🔄 预处理图像：裁剪和缩放...")
            processed_files, parseinfo_path = self.asset_manager.preprocess_frames(
                asset_name=asset_name,
                output_dir=str(result_dir),
                roi_region=[0, 0, 1280, 800],  # 给C++程序的裁剪尺寸
                target_size=[1280, 800]        # 给C++程序的目标尺寸
            )
            self.console.print(f"✅ 预处理完成，生成 {len(processed_files)} 个处理后图像")

            # 调用C++程序
            success, message = self._call_cpp_program(
                kit_info, parseinfo_path, str(result_dir),
                timeout=cpp_config.get("timeout_seconds", 300),
                total_frames=len(frame_files)
            )

            if success:
                self.console.print(f"✅ C++分析完成: {message}", style="green")
                self.processing_results[asset_name] = {
                    "kit_name": kit_name,
                    "result_dir": str(result_dir),
                    "success": True,
                    "message": message
                }
                return True
            else:
                self.console.print(f"❌ C++分析失败: {message}", style="red")
                return False

        except Exception as e:
            self.console.print(f"❌ C++处理异常: {e}", style="red")
            return False

    def _call_cpp_program(self, kit_info: Dict[str, Any], input_file: str,
                         output_dir: str, timeout: int = 300, total_frames: int = 0) -> Tuple[bool, str]:
        """调用C++程序进行分析"""
        try:
            import subprocess
            import signal

            executable_path = kit_info['executable']
            kit_path = kit_info['path']

            if not os.path.exists(executable_path):
                return False, f"可执行文件不存在: {executable_path}"

            # 设置环境变量
            env = os.environ.copy()
            env['LD_LIBRARY_PATH'] = f"{kit_path}:{env.get('LD_LIBRARY_PATH', '')}"

            # 构建命令
            abs_input_file = os.path.abspath(input_file)
            cmd = [executable_path, abs_input_file]

            self.console.print(f"[blue]执行C++程序: {' '.join(cmd)}[/blue]")

            # 启动进程
            start_time = time.time()
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                env=env,
                cwd=kit_path
            )

            # 监控进程并显示进度
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("正在运行C++分析...", total=total_frames)

                processed_frames = 0
                check_interval = 1.0
                elapsed_time = 0

                while elapsed_time < timeout:
                    # 检查进程是否已经结束
                    if process.poll() is not None:
                        processing_time = time.time() - start_time
                        break

                    # 读取输出并解析进度
                    try:
                        line = process.stdout.readline()
                        if line:
                            # 检测帧处理模式：--------[X, timestamp]:path
                            import re
                            frame_match = re.search(r'--------\[(\d+),', line)
                            if frame_match:
                                frame_num = int(frame_match.group(1))
                                processed_frames = max(processed_frames, frame_num + 1)
                                progress.update(task, completed=processed_frames)

                                # 检查是否处理完所有帧
                                if total_frames > 0 and processed_frames >= total_frames:
                                    self.console.print(f"✅ 检测到所有 {total_frames} 帧已处理完成")
                                    time.sleep(3)  # 等待3秒确保后续处理完成
                                    process.send_signal(signal.SIGINT)
                                    try:
                                        process.wait(timeout=10)
                                        processing_time = time.time() - start_time
                                        break
                                    except subprocess.TimeoutExpired:
                                        process.kill()
                                        process.wait()
                                        processing_time = time.time() - start_time
                                        break
                    except:
                        pass

                    time.sleep(check_interval)
                    elapsed_time += check_interval
                else:
                    # 超时处理
                    process.kill()
                    return False, f"C++程序执行超时（{timeout}秒）"

            # 获取输出
            stdout, stderr = process.communicate()

            # 保存日志
            log_file = Path(output_dir) / "cpp_output.log"
            with open(log_file, 'w') as f:
                f.write(f"=== C++程序执行日志 ===\n")
                f.write(f"命令: {' '.join(cmd)}\n")
                f.write(f"执行时间: {processing_time:.2f}秒\n")
                f.write(f"返回码: {process.returncode}\n\n")
                f.write("=== 标准输出 ===\n")
                f.write(stdout)
                f.write("\n=== 标准错误 ===\n")
                f.write(stderr)

            if process.returncode == 0:
                return True, f"处理成功，耗时 {processing_time:.2f}秒"
            else:
                return False, f"C++程序返回错误码 {process.returncode}: {stderr}"

        except Exception as e:
            return False, f"C++程序执行异常: {e}"

    def stage3_video_rendering(self, asset_name: str, output_fps: int = None) -> bool:
        """
        阶段三：视频渲染

        Args:
            asset_name: 资产包名称
            output_fps: 输出帧率

        Returns:
            是否成功
        """
        self.current_stage = "视频渲染"
        self.console.print(f"\n🎥 开始阶段三: {self.current_stage}")

        if output_fps is None:
            output_fps = self.config["rendering_settings"]["default_output_fps"]

        try:
            # 检查是否有C++处理结果
            if asset_name not in self.processing_results:
                self.console.print(f"❌ 没有找到资产包的处理结果: {asset_name}", style="red")
                return False

            result_info = self.processing_results[asset_name]
            result_dir = Path(result_info["result_dir"])
            kit_name = result_info["kit_name"]

            # 创建输出目录
            output_dir = Path(self.config["output_management"]["base_output_dir"])
            output_dir.mkdir(parents=True, exist_ok=True)

            # 生成输出文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            naming_pattern = self.config["output_management"]["naming_convention"]["final_video"]
            output_filename = naming_pattern.format(
                asset_name=asset_name,
                cpp_version=kit_name,
                timestamp=timestamp
            )
            output_path = output_dir / output_filename

            # 渲染视频
            success = self._render_video_with_dms_info(
                asset_name, kit_name, result_dir, output_path, output_fps
            )

            if success:
                self.console.print(f"✅ 视频渲染完成: {output_path}", style="green")
                return True
            else:
                self.console.print("❌ 视频渲染失败", style="red")
                return False

        except Exception as e:
            self.console.print(f"❌ 视频渲染异常: {e}", style="red")
            return False

    def _render_video_with_dms_info(self, asset_name: str, kit_name: str,
                                   result_dir: Path, output_path: Path, fps: float) -> bool:
        """使用DMS信息渲染视频"""
        try:
            import cv2
            import json

            # 获取视频参数
            video_width = 1920
            video_height = 1080

            # 查找JSON文件目录 - C++程序输出的JSON文件位置
            json_dir = Path(self.config["paths"]["cpp_kits_dir"]) / kit_name / "output_json" / "processed_frames"

            if not json_dir.exists():
                self.console.print(f"❌ 找不到JSON输出目录: {json_dir}")
                return False

            # 查找原始图像文件
            original_frames_dir = Path(self.config["paths"]["assets_dir"]) / asset_name / "frames"
            if not original_frames_dir.exists():
                self.console.print(f"❌ 找不到原始图像目录: {original_frames_dir}")
                return False

            # 获取所有JSON文件并排序
            json_files = list(json_dir.glob("*.json"))
            if not json_files:
                self.console.print(f"❌ 在 {json_dir} 中找不到JSON文件")
                return False

            json_files.sort(key=lambda x: x.name)

            self.console.print(f"🎬 开始渲染视频，共 {len(json_files)} 帧")

            # 创建视频写入器
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (video_width, video_height))

            if not out.isOpened():
                self.console.print(f"❌ 无法创建视频写入器")
                return False

            # 渲染每一帧
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=self.console
            ) as progress:
                task = progress.add_task("正在渲染视频帧...", total=len(json_files))

                for i, json_file in enumerate(json_files):
                    try:
                        # 读取JSON数据
                        with open(json_file, 'r', encoding='utf-8') as f:
                            json_data = json.load(f)

                        # 获取对应的原始图像
                        frame_number = i + 1
                        image_file = original_frames_dir / f"{frame_number:06d}.png"

                        if not image_file.exists():
                            # 尝试其他可能的文件名格式
                            image_file = original_frames_dir / f"{frame_number:06d}.jpg"

                        if not image_file.exists():
                            self.console.print(f"⚠️ 找不到图像文件: {image_file}")
                            continue

                        # 加载图像
                        image = cv2.imread(str(image_file))
                        if image is None:
                            self.console.print(f"⚠️ 无法加载图像: {image_file}")
                            continue

                        # 在图像上绘制DMS信息
                        processed_image = self._draw_dms_info_on_image(image.copy(), json_data)

                        # 调整图像大小
                        resized_image = cv2.resize(processed_image, (video_width, video_height))

                        # 写入视频
                        out.write(resized_image)

                        # 更新进度
                        progress.update(task, advance=1)

                    except Exception as e:
                        self.console.print(f"⚠️ 处理帧 {json_file} 时出错: {e}")
                        continue

            # 释放资源
            out.release()

            if output_path.exists():
                self.console.print(f"📹 视频已保存: {output_path}")
                return True
            else:
                self.console.print(f"❌ 视频文件未生成")
                return False

        except Exception as e:
            self.console.print(f"❌ 渲染视频时出错: {e}")
            return False

    def run_pipeline(self, video_path: str, time_range: str,
                    output_fps: int = None, force_recreate: bool = False,
                    cpp_source_path: str = None, skip_cpp_confirm: bool = False) -> bool:
        """
        运行完整的流程
        
        Args:
            video_path: 视频文件路径
            time_range: 时间范围
            output_fps: 输出帧率
            force_recreate: 是否强制重新创建资产包
            
        Returns:
            是否成功
        """
        try:
            self.print_banner()
            
            # 验证输入
            is_valid, message = self.validate_input(video_path, time_range)
            if not is_valid:
                self.console.print(f"❌ 输入验证失败: {message}", style="red")
                return False
            
            # 获取视频信息用于生成资产包名称
            video_info = self.ffmpeg_processor.get_video_info(video_path)

            # 生成资产包名称
            asset_name = self.asset_detector.generate_asset_name(video_path, time_range, video_info)
            self.console.print(f"📦 资产包名称: {asset_name}")
            
            # 阶段一：视频裁剪和帧提取
            stage_config = self.config["stage_control"]["stage1_video_extraction"]
            if stage_config["enabled"]:
                if not self.stage1_video_extraction(video_path, time_range, asset_name, force_recreate):
                    return False
            
            # 阶段二：C++算法分析
            stage_config = self.config["stage_control"]["stage2_cpp_processing"]
            if stage_config["enabled"]:
                if not self.stage2_cpp_processing(asset_name):
                    return False
            
            # 阶段三：视频渲染
            stage_config = self.config["stage_control"]["stage3_video_rendering"]
            if stage_config["enabled"]:
                if not self.stage3_video_rendering(asset_name, output_fps):
                    return False
            
            # 显示完成信息
            elapsed_time = datetime.now() - self.start_time
            self.console.print(f"\n🎉 流程完成！总耗时: {elapsed_time}", style="green bold")
            
            return True
            
        except Exception as e:
            self.console.print(f"❌ 流程执行失败: {e}", style="red")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="DMS端到端一体化分析流程",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python dms_pipeline.py -i video.mp4 -t "00:01:00-00:01:30"
    python dms_pipeline.py -i /path/to/video.mkv -t "00:00:10-00:00:40" --fps 15
    python dms_pipeline.py -i video.avi -t "01:23:45-01:24:15" --force-recreate
        """
    )
    
    parser.add_argument("-i", "--input", required=True,
                       help="输入视频文件路径")
    parser.add_argument("-t", "--time-range", required=True,
                       help="时间范围，格式: HH:MM:SS-HH:MM:SS")
    parser.add_argument("--fps", type=int,
                       help="输出视频帧率 (默认: 10)")
    parser.add_argument("--force-recreate", action="store_true",
                       help="强制重新创建资产包")
    parser.add_argument("--config",
                       help="配置文件路径 (默认: pipeline_config.json)")
    parser.add_argument("--cpp-source",
                       help="C++源文件路径，用于自动拷贝工具包文件")
    parser.add_argument("--skip-cpp-confirm", action="store_true",
                       help="跳过C++处理确认步骤")
    
    args = parser.parse_args()
    
    # 创建流程管理器
    pipeline = DMSPipeline(args.config)
    
    # 运行流程
    success = pipeline.run_pipeline(
        video_path=args.input,
        time_range=args.time_range,
        output_fps=args.fps,
        force_recreate=args.force_recreate,
        cpp_source_path=args.cpp_source,
        skip_cpp_confirm=args.skip_cpp_confirm
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
