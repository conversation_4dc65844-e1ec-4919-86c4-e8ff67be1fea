#!/usr/bin/env python3
"""
智能资产包检测器
根据视频路径和时间段生成资产包名称，并检测已有资产包
"""

import os
import re
import json
from pathlib import Path
from typing import Tuple, Optional, Dict, Any
from datetime import datetime


class AssetDetector:
    """智能资产包检测器"""
    
    def __init__(self, config: Dict[str, Any], assets_dir: Path):
        """
        初始化资产检测器
        
        Args:
            config: pipeline配置
            assets_dir: 资产目录路径
        """
        self.config = config
        self.assets_dir = Path(assets_dir)
        self.input_settings = config.get("input_settings", {})
        
    def parse_time_range(self, time_range: str) -> Tuple[str, str]:
        """
        解析时间范围字符串
        
        Args:
            time_range: 时间范围，格式如 "00:01:00-00:01:30"
            
        Returns:
            (start_time, end_time) 元组
            
        Raises:
            ValueError: 时间格式不正确
        """
        time_format = self.input_settings.get("time_format", "HH:MM:SS-HH:MM:SS")
        
        # 支持多种分隔符
        separators = ['-', '~', 'to', '至']
        pattern = f"({'|'.join(re.escape(sep) for sep in separators)})"
        
        parts = re.split(pattern, time_range, 1)
        if len(parts) < 3:
            raise ValueError(f"时间范围格式错误: {time_range}，期望格式: {time_format}")
        
        start_time = parts[0].strip()
        end_time = parts[2].strip()
        
        # 验证时间格式
        time_pattern = r'^\d{2}:\d{2}:\d{2}$'
        if not re.match(time_pattern, start_time) or not re.match(time_pattern, end_time):
            raise ValueError(f"时间格式错误，期望格式: HH:MM:SS")
        
        return start_time, end_time
    
    def format_time_for_name(self, time_str: str) -> str:
        """
        将时间字符串格式化为文件名格式
        
        Args:
            time_str: 时间字符串，如 "00:01:00"
            
        Returns:
            格式化后的时间字符串，如 "000100"
        """
        return time_str.replace(':', '')
    
    def extract_video_name(self, video_path: str) -> str:
        """
        从视频路径提取视频名称（不含扩展名）
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频名称
        """
        video_file = Path(video_path)
        return video_file.stem
    
    def generate_asset_name(self, video_path: str, time_range: str,
                          video_info: Dict[str, Any] = None) -> str:
        """
        根据视频路径和时间范围生成资产包名称

        Args:
            video_path: 视频文件路径
            time_range: 时间范围
            video_info: 视频信息（宽度、高度等）

        Returns:
            资产包名称
        """
        try:
            video_name = self.extract_video_name(video_path)
            start_time, end_time = self.parse_time_range(time_range)

            start_formatted = self.format_time_for_name(start_time)
            end_formatted = self.format_time_for_name(end_time)

            # 默认视频信息
            if video_info is None:
                video_info = {
                    "width": 1920,
                    "height": 1080,
                    "roi_x": 0,
                    "roi_y": 0
                }

            # 生成时间戳（用于唯一性）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 使用配置中的命名模式
            pattern = self.input_settings.get("asset_naming_pattern",
                                             "{video_name}_{start_time}_{end_time}_{width}_{height}_{roi_x}_{roi_y}_{timestamp}")

            asset_name = pattern.format(
                video_name=video_name,
                start_time=start_formatted,
                end_time=end_formatted,
                width=video_info.get("width", 1920),
                height=video_info.get("height", 1080),
                roi_x=video_info.get("roi_x", 0),
                roi_y=video_info.get("roi_y", 0),
                timestamp=timestamp
            )

            return asset_name

        except Exception as e:
            raise ValueError(f"生成资产包名称失败: {e}")
    
    def check_asset_exists(self, asset_name: str) -> Tuple[bool, Optional[Path]]:
        """
        检查资产包是否已存在
        
        Args:
            asset_name: 资产包名称
            
        Returns:
            (exists, asset_path) 元组
        """
        asset_path = self.assets_dir / asset_name
        
        if not asset_path.exists():
            return False, None
        
        # 检查必要文件是否存在
        metadata_file = asset_path / "metadata.json"
        frames_dir = asset_path / "frames"
        
        if not metadata_file.exists():
            return False, None
        
        if not frames_dir.exists() or not any(frames_dir.iterdir()):
            return False, None
        
        return True, asset_path
    
    def validate_existing_asset(self, asset_path: Path, 
                              video_path: str, time_range: str) -> Tuple[bool, str]:
        """
        验证已存在的资产包是否匹配当前请求
        
        Args:
            asset_path: 资产包路径
            video_path: 视频文件路径
            time_range: 时间范围
            
        Returns:
            (is_valid, message) 元组
        """
        try:
            metadata_file = asset_path / "metadata.json"
            
            with open(metadata_file, 'r', encoding='utf-8') as f:
                metadata = json.load(f)
            
            # 检查源视频信息
            source_info = metadata.get("source_info", {})
            stored_video_path = source_info.get("video_path", "")
            
            # 检查时间范围
            extraction_info = metadata.get("extraction_info", {})
            time_range_info = extraction_info.get("time_range", {})
            stored_start = time_range_info.get("start_time", "")
            stored_end = time_range_info.get("end_time", "")
            
            start_time, end_time = self.parse_time_range(time_range)
            
            # 比较路径（使用绝对路径）
            current_video_abs = str(Path(video_path).resolve())
            stored_video_abs = str(Path(stored_video_path).resolve()) if stored_video_path else ""
            
            if current_video_abs != stored_video_abs:
                return False, f"视频路径不匹配: 当前={current_video_abs}, 存储={stored_video_abs}"
            
            if start_time != stored_start or end_time != stored_end:
                return False, f"时间范围不匹配: 当前={start_time}-{end_time}, 存储={stored_start}-{stored_end}"
            
            # 检查帧文件数量
            frames_dir = asset_path / "frames"
            frame_count = len(list(frames_dir.glob("*.png")) + list(frames_dir.glob("*.jpg")))
            
            if frame_count == 0:
                return False, "资产包中没有帧文件"
            
            return True, f"资产包有效，包含 {frame_count} 个帧文件"
            
        except Exception as e:
            return False, f"验证资产包时出错: {e}"
    
    def detect_asset(self, video_path: str, time_range: str) -> Dict[str, Any]:
        """
        检测资产包状态
        
        Args:
            video_path: 视频文件路径
            time_range: 时间范围
            
        Returns:
            检测结果字典
        """
        try:
            # 生成资产包名称
            asset_name = self.generate_asset_name(video_path, time_range)
            
            # 检查是否存在
            exists, asset_path = self.check_asset_exists(asset_name)
            
            result = {
                "asset_name": asset_name,
                "exists": exists,
                "asset_path": str(asset_path) if asset_path else None,
                "can_skip": False,
                "message": "",
                "video_path": video_path,
                "time_range": time_range
            }
            
            if exists:
                # 验证已存在的资产包
                is_valid, message = self.validate_existing_asset(asset_path, video_path, time_range)
                result["can_skip"] = is_valid
                result["message"] = message
            else:
                result["message"] = "资产包不存在，需要创建"
            
            return result
            
        except Exception as e:
            return {
                "asset_name": "",
                "exists": False,
                "asset_path": None,
                "can_skip": False,
                "message": f"检测失败: {e}",
                "video_path": video_path,
                "time_range": time_range,
                "error": str(e)
            }


def test_asset_detector():
    """测试资产检测器"""
    config = {
        "input_settings": {
            "asset_naming_pattern": "{video_name}_{start_time}_{end_time}",
            "time_format": "HH:MM:SS-HH:MM:SS"
        }
    }
    
    detector = AssetDetector(config, Path("../assets"))
    
    # 测试用例
    test_cases = [
        ("/path/to/video.mp4", "00:01:00-00:01:30"),
        ("/path/to/test_video.mkv", "00:00:10-00:00:40"),
        ("video_001.avi", "01:23:45-01:24:15")
    ]
    
    for video_path, time_range in test_cases:
        print(f"\n测试: {video_path} {time_range}")
        result = detector.detect_asset(video_path, time_range)
        print(f"结果: {result}")


if __name__ == "__main__":
    test_asset_detector()
