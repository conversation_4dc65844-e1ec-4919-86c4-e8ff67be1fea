#!/usr/bin/env python3
"""
配置文件验证器 - 智能检查和交互确认缺失的配置文件

功能：
1. 自动检测C++工具包中缺失的配置文件
2. 提供交互式配置文件创建
3. 验证远端服务连接
4. 对比模型文件一致性
"""

import os
import json
import socket
import hashlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt, Confirm
from rich.table import Table

console = Console()

class ConfigValidator:
    """配置文件验证器"""

    def __init__(self, cpp_kit_path: str, interactive: bool = True):
        self.cpp_kit_path = Path(cpp_kit_path)
        self.interactive = interactive  # 是否启用交互模式
        self.required_files = {
            "ip_port.json": "远端服务配置",
            "calidata.json": "相机校准数据"
        }
        self.optional_files = {
            "config.json": "其他配置参数"
        }

        # 必需字段定义
        self.required_fields = {
            'ip_port.json': ['ip', 'port'],
            'calidata.json': ['head_yaw', 'head_pitch', 'head_roll', 'left_eye_yaw', 'left_eye_pitch', 'right_eye_yaw', 'right_eye_pitch']
        }
        
    def check_missing_files(self) -> Dict[str, bool]:
        """检查缺失的配置文件"""
        missing_files = {}
        
        for file_name, description in self.required_files.items():
            file_path = self.cpp_kit_path / file_name
            missing_files[file_name] = not file_path.exists()
            
        return missing_files

    def validate_config_files_with_confirmation(self, skip_check: bool = False) -> Tuple[bool, str]:
        """智能检查与确认配置文件

        Args:
            skip_check: 是否跳过交互式检查

        Returns:
            Tuple[bool, str]: (是否验证成功, 结果信息)
        """
        if skip_check:
            console.print("⏭️ 跳过配置文件检查")
            return True, "跳过配置文件检查"

        console.print(Panel(
            "🔧 配置文件智能检查与确认",
            title="配置验证器",
            border_style="blue"
        ))

        validation_results = []

        for config_file in self.required_files.keys():
            config_path = self.cpp_kit_path / config_file

            console.print(f"\n📋 检查配置文件: {config_file}")

            if config_path.exists():
                # 文件存在，验证并确认
                success, message = self._validate_existing_config(config_path, config_file)
                if not success:
                    return False, message
                validation_results.append(f"✅ {config_file}: 验证通过")
            else:
                # 文件不存在，询问是否创建
                success, message = self._handle_missing_config(config_path, config_file)
                if not success:
                    return False, message
                validation_results.append(f"✅ {config_file}: 已创建")

        result_msg = "配置文件验证完成:\n" + "\n".join(validation_results)
        return True, result_msg

    def _validate_existing_config(self, config_path: Path, config_file: str) -> Tuple[bool, str]:
        """验证现有配置文件"""
        try:
            # 读取并解析JSON
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 验证JSON格式和必需字段
            validation_success, validation_msg = self._validate_config_structure(config_data, config_file)
            if not validation_success:
                console.print(f"❌ 配置文件格式错误: {validation_msg}")
                return False, f"配置文件 {config_file} 格式错误: {validation_msg}"

            # 显示配置内容
            self._display_config_content(config_data, config_file)

            # 询问用户确认
            if not Confirm.ask(f"✅ 配置文件 {config_file} 格式正确，是否确认使用此配置？"):
                # 用户不确认，询问是否重新配置
                if Confirm.ask("是否重新配置此文件？"):
                    return self._recreate_config_file(config_path, config_file)
                else:
                    return False, f"用户取消了 {config_file} 的配置确认"

            return True, f"{config_file} 配置确认成功"

        except json.JSONDecodeError as e:
            console.print(f"❌ JSON格式错误: {e}")
            if Confirm.ask("配置文件JSON格式错误，是否重新创建？"):
                return self._recreate_config_file(config_path, config_file)
            return False, f"{config_file} JSON格式错误"

        except Exception as e:
            console.print(f"❌ 读取配置文件失败: {e}")
            return False, f"读取 {config_file} 失败: {e}"

    def _handle_missing_config(self, config_path: Path, config_file: str) -> Tuple[bool, str]:
        """处理缺失的配置文件"""
        console.print(f"⚠️ 配置文件 {config_file} 不存在")

        # 显示默认配置模板
        if config_file == 'ip_port.json':
            template = self.create_ip_port_template()
        elif config_file == 'calidata.json':
            template = self.create_calidata_template()
        else:
            return False, f"未知的配置文件类型: {config_file}"

        self._display_config_content(template, f"{config_file} (默认模板)")

        choice = Prompt.ask(
            "请选择操作",
            choices=["default", "custom", "skip"],
            default="default"
        )

        if choice == "default":
            # 使用默认配置
            return self._create_config_from_template(config_path, config_file, template)
        elif choice == "custom":
            # 自定义配置
            return self._create_custom_config(config_path, config_file)
        else:
            # 跳过
            return False, f"用户跳过了 {config_file} 的配置"

    def create_ip_port_template(self) -> Dict:
        """创建ip_port.json模板"""
        return {
            "ip": "***********",
            "port": 1180
        }

    def _validate_config_structure(self, config_data: Dict, config_file: str) -> Tuple[bool, str]:
        """验证配置文件结构"""
        required_fields = self.required_fields.get(config_file, [])

        missing_fields = []
        for field in required_fields:
            if field not in config_data:
                missing_fields.append(field)

        if missing_fields:
            return False, f"缺少必需字段: {', '.join(missing_fields)}"

        # 特定配置文件的额外验证
        if config_file == 'ip_port.json':
            return self._validate_ip_port_config(config_data)
        elif config_file == 'calidata.json':
            return self._validate_calidata_config(config_data)

        return True, "配置结构验证通过"

    def _validate_ip_port_config(self, config_data: Dict) -> Tuple[bool, str]:
        """验证IP端口配置"""
        try:
            # 验证端口范围
            remote_port = config_data.get('remote_port')
            if not isinstance(remote_port, int) or not (1 <= remote_port <= 65535):
                return False, "remote_port 必须是1-65535之间的整数"

            # 验证主机地址格式（简单检查）
            remote_host = config_data.get('remote_host', '')
            if not remote_host or not isinstance(remote_host, str):
                return False, "remote_host 必须是有效的主机地址"

            return True, "IP端口配置验证通过"

        except Exception as e:
            return False, f"IP端口配置验证失败: {e}"

    def _validate_calidata_config(self, config_data: Dict) -> Tuple[bool, str]:
        """验证相机标定数据配置"""
        try:
            # 验证相机矩阵
            camera_matrix = config_data.get('camera_matrix')
            if not isinstance(camera_matrix, list) or len(camera_matrix) != 3:
                return False, "camera_matrix 必须是3x3矩阵"

            for row in camera_matrix:
                if not isinstance(row, list) or len(row) != 3:
                    return False, "camera_matrix 每行必须包含3个元素"

            # 验证畸变系数
            distortion_coeffs = config_data.get('distortion_coeffs')
            if not isinstance(distortion_coeffs, list) or len(distortion_coeffs) != 5:
                return False, "distortion_coeffs 必须包含5个元素"

            # 验证图像尺寸
            image_size = config_data.get('image_size')
            if not isinstance(image_size, list) or len(image_size) != 2:
                return False, "image_size 必须包含宽度和高度两个值"

            return True, "相机标定数据配置验证通过"

        except Exception as e:
            return False, f"相机标定数据配置验证失败: {e}"

    def _display_config_content(self, config_data: Dict, title: str) -> None:
        """显示配置文件内容"""
        from rich.syntax import Syntax

        json_content = json.dumps(config_data, indent=2, ensure_ascii=False)
        syntax = Syntax(json_content, "json", theme="monokai", line_numbers=True)

        console.print(Panel(
            syntax,
            title=f"📄 {title}",
            border_style="green"
        ))

    def _create_config_from_template(self, config_path: Path, config_file: str, template: Dict) -> Tuple[bool, str]:
        """从模板创建配置文件"""
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)

            console.print(f"✅ 已创建默认配置文件: {config_file}")
            return True, f"成功创建 {config_file}"

        except Exception as e:
            return False, f"创建 {config_file} 失败: {e}"

    def _recreate_config_file(self, config_path: Path, config_file: str) -> Tuple[bool, str]:
        """重新创建配置文件"""
        choice = Prompt.ask(
            f"重新配置 {config_file}",
            choices=["default", "custom"],
            default="default"
        )

        if choice == "default":
            if config_file == 'ip_port.json':
                template = self.create_ip_port_template()
            elif config_file == 'calidata.json':
                template = self.create_calidata_template()
            else:
                return False, f"未知的配置文件类型: {config_file}"
            return self._create_config_from_template(config_path, config_file, template)
        else:
            return self._create_custom_config(config_path, config_file)

    def _create_custom_config(self, config_path: Path, config_file: str) -> Tuple[bool, str]:
        """创建自定义配置文件"""
        console.print(f"📝 自定义配置 {config_file}")

        if config_file == 'ip_port.json':
            template = self.create_ip_port_template()
            # IP端口配置
            template['remote_host'] = Prompt.ask("远程主机地址", default=template['remote_host'])
            template['remote_port'] = int(Prompt.ask("远程端口", default=str(template['remote_port'])))
            template['ssh_host'] = Prompt.ask("SSH主机地址", default=template['ssh_host'])
            template['ssh_user'] = Prompt.ask("SSH用户名", default=template['ssh_user'])
            template['ssh_password'] = Prompt.ask("SSH密码", default=template['ssh_password'], password=True)

        elif config_file == 'calidata.json':
            template = self.create_calidata_template()
            # 相机标定数据配置
            console.print("相机标定数据配置较为复杂，建议使用默认值或从其他工具导入")
            if not Confirm.ask("是否使用默认的相机标定数据？"):
                console.print("请手动编辑配置文件或从标定工具导入数据")
        else:
            return False, f"未知的配置文件类型: {config_file}"

        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(template, f, indent=2, ensure_ascii=False)

            console.print(f"✅ 已创建自定义配置文件: {config_file}")
            return True, f"成功创建自定义 {config_file}"

        except Exception as e:
            return False, f"创建自定义 {config_file} 失败: {e}"

    def create_calidata_template(self) -> Dict:
        """创建calidata.json模板"""
        return {
            "head_yaw": 28.45,
            "head_pitch": 26.88,
            "head_roll": 2.68,
            "left_eye_yaw": 6.19,
            "left_eye_pitch": 8.57,
            "right_eye_yaw": 13.67,
            "right_eye_pitch": 6.75,
            "left_eye_curve_mean": 0.003146,
            "right_eye_curve_mean": 0.002944
        }
    
    def interactive_config_creation(self) -> bool:
        """交互式配置文件创建"""
        console.print(Panel(
            "🔧 配置文件检查与创建\n"
            "检测到缺失的配置文件，将引导您创建必要的配置。",
            title="配置验证器",
            border_style="blue"
        ))
        
        missing_files = self.check_missing_files()
        
        if not any(missing_files.values()):
            console.print("✅ 所有必需的配置文件都存在")
            return True
        
        # 显示缺失文件表格
        table = Table(title="缺失的配置文件")
        table.add_column("文件名", style="cyan")
        table.add_column("描述", style="magenta")
        table.add_column("状态", style="red")
        
        for file_name, is_missing in missing_files.items():
            if is_missing:
                description = self.required_files.get(file_name, "配置文件")
                table.add_row(file_name, description, "❌ 缺失")
        
        console.print(table)
        
        # 询问是否创建配置文件
        if not Confirm.ask("\n是否要创建缺失的配置文件？"):
            console.print("❌ 用户取消配置创建")
            return False
        
        # 创建每个缺失的配置文件
        for file_name, is_missing in missing_files.items():
            if is_missing:
                if not self._create_config_file(file_name):
                    return False
        
        console.print("✅ 所有配置文件创建完成")
        return True
    
    def _create_config_file(self, file_name: str) -> bool:
        """创建单个配置文件"""
        console.print(f"\n📝 创建配置文件: {file_name}")
        
        if file_name == "ip_port.json":
            return self._create_ip_port_config()
        elif file_name == "calidata.json":
            return self._create_calidata_config()
        else:
            console.print(f"❌ 未知的配置文件类型: {file_name}")
            return False
    
    def _create_ip_port_config(self) -> bool:
        """创建ip_port.json配置"""
        template = self.create_ip_port_template()
        
        console.print("请输入远端DMS服务配置:")
        
        # 获取用户输入
        ip = Prompt.ask("IP地址", default=template["ip"])
        port = Prompt.ask("端口", default=str(template["port"]))
        
        try:
            port = int(port)
        except ValueError:
            console.print("❌ 端口必须是数字")
            return False
        
        # 创建配置
        config = {
            "ip": ip,
            "port": port,
            "timeout": template["timeout"],
            "description": template["description"]
        }
        
        # 保存文件
        config_path = self.cpp_kit_path / "ip_port.json"
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            console.print(f"✅ 已创建: {config_path}")
            return True
        except Exception as e:
            console.print(f"❌ 创建失败: {e}")
            return False
    
    def _create_calidata_config(self) -> bool:
        """创建calidata.json配置"""
        template = self.create_calidata_template()
        
        console.print("相机校准数据配置:")
        console.print("1. 使用默认模板（适用于测试）")
        console.print("2. 手动输入参数")
        
        choice = Prompt.ask("请选择", choices=["1", "2"], default="1")
        
        if choice == "1":
            config = template
        else:
            # 简化的手动输入（实际使用中可以更详细）
            console.print("请输入相机参数（简化版）:")
            fx = float(Prompt.ask("焦距fx", default="800.0"))
            fy = float(Prompt.ask("焦距fy", default="800.0"))
            cx = float(Prompt.ask("主点cx", default="320.0"))
            cy = float(Prompt.ask("主点cy", default="240.0"))
            
            config = template.copy()
            config["camera_matrix"] = [
                [fx, 0.0, cx],
                [0.0, fy, cy],
                [0.0, 0.0, 1.0]
            ]
        
        # 保存文件
        config_path = self.cpp_kit_path / "calidata.json"
        try:
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            console.print(f"✅ 已创建: {config_path}")
            return True
        except Exception as e:
            console.print(f"❌ 创建失败: {e}")
            return False
    
    def verify_remote_service(self) -> bool:
        """验证远端服务连接"""
        # 1. 验证DMS服务端口连接
        ip_port_file = self.cpp_kit_path / "ip_port.json"

        if not ip_port_file.exists():
            console.print("❌ ip_port.json文件不存在")
            return False

        try:
            with open(ip_port_file, 'r') as f:
                config = json.load(f)

            ip = config.get("ip")
            port = config.get("port")
            timeout = config.get("timeout", 10)

            console.print(f"🔍 验证DMS服务: {ip}:{port}")

            # 测试DMS服务端口连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)

            result = sock.connect_ex((ip, port))
            sock.close()

            if result == 0:
                console.print("✅ DMS服务连接成功")
            else:
                console.print(f"⚠️ DMS服务连接失败 (错误码: {result})")
                # DMS服务连接失败不阻止继续，可能服务未启动

            # 2. 完整的远程验证流程
            ssh_host = "***********"
            ssh_user = "root"
            ssh_pass = "root"

            console.print(f"🔍 验证SSH连接: {ssh_user}@{ssh_host}")

            # 使用subprocess测试SSH连接
            import subprocess
            ssh_cmd = [
                "sshpass", "-p", ssh_pass,
                "ssh", "-o", "ConnectTimeout=10",
                "-o", "StrictHostKeyChecking=no",
                f"{ssh_user}@{ssh_host}",
                "echo 'SSH连接测试成功'"
            ]

            try:
                result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    console.print("✅ SSH连接验证成功")
                    # 执行完整的远程模型同步流程
                    return self._perform_remote_model_sync(ssh_host, ssh_user, ssh_pass)
                else:
                    console.print(f"❌ SSH连接失败: {result.stderr}")
                    return False
            except subprocess.TimeoutExpired:
                console.print("❌ SSH连接超时")
                return False
            except FileNotFoundError:
                console.print("⚠️ sshpass未安装，跳过SSH验证")
                # 如果没有sshpass，只验证DMS服务端口
                return result == 0

        except Exception as e:
            console.print(f"❌ 验证远端服务时出错: {e}")
            return False
    
    def verify_model_files(self) -> bool:
        """验证模型文件完整性"""
        # 读取metadata.json获取模型文件列表
        metadata_path = self.cpp_kit_path / "metadata.json"
        if not metadata_path.exists():
            console.print("❌ metadata.json文件不存在")
            return False

        try:
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)

            # 获取模型文件列表，支持两种格式
            model_files = []
            if "model_files" in metadata:
                model_files = metadata["model_files"]
            elif "models" in metadata:
                model_files = metadata["models"]
            else:
                console.print("❌ metadata.json中未找到模型文件配置")
                return False

            console.print("🔍 验证模型文件:")

            for model_file in model_files:
                # 支持相对路径和绝对路径
                model_path = self.cpp_kit_path / model_file
                if model_path.exists():
                    # 计算MD5
                    md5_hash = self._calculate_md5(model_path)
                    file_size = model_path.stat().st_size
                    console.print(f"  ✅ {model_file}: {file_size} bytes, MD5: {md5_hash[:8]}...")
                else:
                    console.print(f"  ❌ {model_file}: 文件缺失")
                    return False

            return True

        except Exception as e:
            console.print(f"❌ 读取metadata.json时出错: {e}")
            return False
    
    def _calculate_md5(self, file_path: Path) -> str:
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def full_validation(self) -> bool:
        """完整验证流程"""
        console.print(Panel(
            "开始C++工具包配置验证流程",
            title="配置验证器",
            border_style="green"
        ))
        
        # 1. 检查和创建配置文件
        if not self.interactive_config_creation():
            return False
        
        # 2. 验证模型文件
        if not self.verify_model_files():
            console.print("❌ 模型文件验证失败")
            return False
        
        # 3. 验证远端服务（必需）
        console.print("🌐 验证远端服务连接...")
        if True:  # 自动进行远端验证
            if not self.verify_remote_service():
                console.print("❌ 远端服务验证失败，无法继续处理")
                return False

        console.print(Panel(
            "✅ 配置验证完成，可以开始处理流程",
            title="验证成功",
            border_style="green"
        ))

        return True

    def _perform_remote_model_sync(self, ssh_host: str, ssh_user: str, ssh_pass: str) -> bool:
        """执行完整的远程模型同步流程

        Args:
            ssh_host: SSH主机地址
            ssh_user: SSH用户名
            ssh_pass: SSH密码

        Returns:
            bool: 同步是否成功
        """
        import subprocess
        import hashlib
        from pathlib import Path

        console.print("🔄 开始远程模型同步流程...")

        try:
            # 第二步：校验远程主机模型文件MD5值
            console.print("📋 第二步：校验远程模型文件MD5值...")

            local_models_dir = self.cpp_kit_path  # 模型文件直接在工具包根目录
            remote_models_dir = "/userfs/model"

            model_files = ["eye.ovm", "FaceDetection.ovm", "FaceKeypoints.ovm"]
            models_need_sync = []

            for model_file in model_files:
                local_file = local_models_dir / model_file
                if not local_file.exists():
                    console.print(f"❌ 本地模型文件不存在: {local_file}")
                    continue

                # 计算本地文件MD5
                local_md5 = self._calculate_file_md5(local_file)

                # 获取远程文件MD5
                remote_md5_cmd = [
                    "sshpass", "-p", ssh_pass,
                    "ssh", "-o", "ConnectTimeout=10",
                    "-o", "StrictHostKeyChecking=no",
                    f"{ssh_user}@{ssh_host}",
                    f"md5sum {remote_models_dir}/{model_file} 2>/dev/null | cut -d' ' -f1"
                ]

                result = subprocess.run(remote_md5_cmd, capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    remote_md5 = result.stdout.strip()
                    if local_md5 == remote_md5:
                        console.print(f"✅ {model_file}: MD5一致 ({local_md5[:8]}...)")
                    else:
                        console.print(f"⚠️ {model_file}: MD5不一致 (本地:{local_md5[:8]}... 远程:{remote_md5[:8]}...)")
                        models_need_sync.append((model_file, local_file))
                else:
                    console.print(f"⚠️ {model_file}: 远程文件不存在或无法访问")
                    models_need_sync.append((model_file, local_file))

            # 第三步：上传不一致的模型文件
            if models_need_sync:
                console.print(f"📤 第三步：上传 {len(models_need_sync)} 个模型文件...")

                for model_file, local_file in models_need_sync:
                    console.print(f"📤 上传 {model_file}...")

                    # 使用scp上传文件
                    scp_cmd = [
                        "sshpass", "-p", ssh_pass,
                        "scp", "-o", "ConnectTimeout=30",
                        "-o", "StrictHostKeyChecking=no",
                        str(local_file),
                        f"{ssh_user}@{ssh_host}:{remote_models_dir}/{model_file}"
                    ]

                    result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=60)
                    if result.returncode == 0:
                        console.print(f"✅ {model_file} 上传成功")
                    else:
                        console.print(f"❌ {model_file} 上传失败: {result.stderr}")
                        return False

                # 第四步：重启远程服务（模型文件已更新）
                console.print("🔄 第四步：重启远程DMS服务...")

                # 先停止现有服务
                stop_cmd = [
                    "sshpass", "-p", ssh_pass,
                    "ssh", "-o", "ConnectTimeout=10",
                    "-o", "StrictHostKeyChecking=no",
                    f"{ssh_user}@{ssh_host}",
                    "ps aux | grep tx_dms_oax_test_tool_update | grep -v grep | awk '{print $2}' | xargs kill -9 2>/dev/null || true"
                ]
                subprocess.run(stop_cmd, capture_output=True, text=True, timeout=15)

                # 等待进程完全停止
                import time
                time.sleep(2)

                # 重新启动服务
                restart_cmd = [
                    "sshpass", "-p", ssh_pass,
                    "ssh", "-o", "ConnectTimeout=10",
                    "-o", "StrictHostKeyChecking=no",
                    f"{ssh_user}@{ssh_host}",
                    "cd /userfs && nohup ./tx_dms_oax_test_tool_update > /dev/null 2>&1 &"
                ]

                result = subprocess.run(restart_cmd, capture_output=True, text=True, timeout=20)
                if result.returncode == 0:
                    console.print("✅ 远程DMS服务重启成功")
                else:
                    console.print(f"⚠️ 远程DMS服务重启可能失败: {result.stderr}")
            else:
                # 第五步：确保远程服务运行
                console.print("✅ 第三步：模型文件已同步，无需上传")
                console.print("🔍 第五步：确保远程DMS服务运行...")

                # 使用ps命令检查服务状态（兼容性更好）
                check_service_cmd = [
                    "sshpass", "-p", ssh_pass,
                    "ssh", "-o", "ConnectTimeout=10",
                    "-o", "StrictHostKeyChecking=no",
                    f"{ssh_user}@{ssh_host}",
                    "ps aux | grep tx_dms_oax_test_tool_update | grep -v grep"
                ]

                result = subprocess.run(check_service_cmd, capture_output=True, text=True, timeout=15)
                if result.returncode == 0 and result.stdout.strip():
                    console.print("✅ 远程DMS服务正在运行")
                else:
                    console.print("🔄 启动远程DMS服务...")
                    start_cmd = [
                        "sshpass", "-p", ssh_pass,
                        "ssh", "-o", "ConnectTimeout=10",
                        "-o", "StrictHostKeyChecking=no",
                        f"{ssh_user}@{ssh_host}",
                        "cd /userfs && nohup ./tx_dms_oax_test_tool_update > /dev/null 2>&1 &"
                    ]

                    result = subprocess.run(start_cmd, capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        console.print("✅ 远程DMS服务启动成功")
                        # 等待服务启动并验证端口连接
                        import time
                        time.sleep(3)

                        # 重新验证DMS服务连接
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(5)
                        port_result = sock.connect_ex((ssh_host, 1180))
                        sock.close()

                        if port_result == 0:
                            console.print("✅ DMS服务端口验证成功")
                        else:
                            console.print(f"⚠️ DMS服务端口仍无法连接 (错误码: {port_result})")
                    else:
                        console.print(f"⚠️ 远程DMS服务启动可能失败: {result.stderr}")

            console.print("✅ 远程模型同步流程完成")
            return True

        except Exception as e:
            console.print(f"❌ 远程模型同步失败: {e}")
            return False

    def _calculate_file_md5(self, file_path: Path) -> str:
        """计算文件MD5值

        Args:
            file_path: 文件路径

        Returns:
            str: MD5值
        """
        import hashlib

        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()


def main():
    """主函数 - 用于独立测试"""
    import sys
    
    if len(sys.argv) != 2:
        print("用法: python config_validator.py <cpp_kit_path>")
        sys.exit(1)
    
    cpp_kit_path = sys.argv[1]
    validator = ConfigValidator(cpp_kit_path)
    
    if validator.full_validation():
        print("✅ 验证成功")
        sys.exit(0)
    else:
        print("❌ 验证失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
