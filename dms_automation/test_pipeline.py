#!/usr/bin/env python3
"""
DMS端到端一体化分析流程测试脚本
"""

import sys
import os
from pathlib import Path

# 添加scripts目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "scripts"))

from dms_pipeline import DMSPipeline


def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试DMS端到端一体化分析流程")
    
    try:
        # 初始化流程管理器
        pipeline = DMSPipeline()
        print("✅ 流程管理器初始化成功")
        
        # 测试配置加载
        config = pipeline.config
        print(f"✅ 配置加载成功，包含 {len(config)} 个配置项")
        
        # 测试目录创建
        pipeline.setup_directories()
        print("✅ 目录创建成功")
        
        # 测试资产检测器
        detector = pipeline.asset_detector
        print("✅ 资产检测器初始化成功")
        
        # 测试输入验证
        test_cases = [
            ("test.mp4", "00:01:00-00:01:30", False),  # 文件不存在
            ("", "00:01:00-00:01:30", False),          # 空路径
            ("test.mp4", "invalid-time", False),       # 时间格式错误
        ]
        
        for video_path, time_range, expected in test_cases:
            is_valid, message = pipeline.validate_input(video_path, time_range)
            if is_valid == expected:
                print(f"❌ 输入验证测试失败: {video_path}, {time_range}")
            else:
                print(f"✅ 输入验证测试通过: {message}")
        
        print("\n🎉 基础功能测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_asset_naming():
    """测试资产命名功能"""
    print("\n🧪 测试资产命名功能")
    
    try:
        pipeline = DMSPipeline()
        detector = pipeline.asset_detector
        
        # 测试资产名称生成
        test_video_info = {
            "width": 1920,
            "height": 1080,
            "roi_x": 0,
            "roi_y": 0
        }
        
        asset_name = detector.generate_asset_name(
            "test_video.mp4", 
            "00:01:00-00:01:30",
            test_video_info
        )
        
        print(f"✅ 生成的资产名称: {asset_name}")
        
        # 验证命名格式
        if "test_video" in asset_name and "000100" in asset_name and "000130" in asset_name:
            print("✅ 资产命名格式正确")
        else:
            print("❌ 资产命名格式错误")
            
        return True
        
    except Exception as e:
        print(f"❌ 资产命名测试失败: {e}")
        return False


def test_configuration():
    """测试配置系统"""
    print("\n🧪 测试配置系统")
    
    try:
        pipeline = DMSPipeline()
        config = pipeline.config
        
        # 检查必要的配置项
        required_sections = [
            "pipeline_info",
            "input_settings", 
            "stage_control",
            "extraction_settings",
            "cpp_processing",
            "rendering_settings",
            "output_management",
            "paths"
        ]
        
        for section in required_sections:
            if section in config:
                print(f"✅ 配置节存在: {section}")
            else:
                print(f"❌ 配置节缺失: {section}")
                return False
        
        # 检查路径配置
        paths = config["paths"]
        for path_name, path_value in paths.items():
            path_obj = Path(path_value)
            if path_obj.exists():
                print(f"✅ 路径存在: {path_name} -> {path_value}")
            else:
                print(f"⚠️ 路径不存在: {path_name} -> {path_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("DMS端到端一体化分析流程 - 测试套件")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_asset_naming,
        test_configuration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {test_func.__name__} - {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序准备就绪。")
        print("\n📝 使用方法:")
        print("python dms_pipeline.py -i /path/to/video.mp4 -t \"00:01:00-00:01:30\"")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
