{"pipeline_info": {"name": "DMS端到端一体化分析流程", "version": "1.0.0", "description": "从视频裁剪到最终渲染的完整自动化流程"}, "input_settings": {"supported_video_formats": [".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv"], "time_format": "HH:MM:SS-HH:MM:SS", "asset_naming_pattern": "{video_name}_{start_time}_{end_time}_{width}_{height}_{roi_x}_{roi_y}_{timestamp}", "time_format_in_name": "HHMMSS", "include_video_info": true, "include_timestamp": true}, "stage_control": {"stage1_video_extraction": {"enabled": true, "skip_if_exists": true, "description": "视频裁剪和帧提取阶段"}, "stage2_cpp_processing": {"enabled": true, "require_confirmation": true, "description": "C++算法分析阶段"}, "stage3_video_rendering": {"enabled": true, "description": "视频渲染阶段"}}, "extraction_settings": {"preserve_original_fps": true, "output_format": "png", "quality_settings": {"compression": "lossless", "color_space": "RGB"}, "roi_support": true, "default_roi": {"x": 0, "y": 0, "width": "auto", "height": "auto"}}, "cpp_processing": {"config_validation": true, "timeout_seconds": 300, "max_workers": "auto", "error_handling": {"on_failure": "terminate", "retry_count": 0, "log_level": "detailed"}}, "rendering_settings": {"output_fps_options": [10, 15, 24, 30], "default_output_fps": 10, "output_format": "mp4", "quality": {"bitrate": "2M", "preset": "medium", "crf": 23}, "comparison_video": {"enabled": true, "layout": "side_by_side", "include_original": true}}, "output_management": {"base_output_dir": "../pipeline_outputs", "directory_structure": {"use_timestamp": true, "organize_by_date": true, "create_subdirs": true}, "naming_convention": {"final_video": "{asset_name}_{cpp_version}_{timestamp}.mp4", "comparison_video": "{asset_name}_comparison_{timestamp}.mp4", "analysis_report": "{asset_name}_report_{timestamp}.json"}}, "intermediate_files": {"preserve_asset_packages": true, "preserve_json_results": true, "preserve_temp_files": false, "cleanup_on_success": false, "cleanup_on_failure": true, "auto_cleanup_days": 7}, "logging": {"level": "INFO", "console_output": true, "file_output": true, "log_file_pattern": "pipeline_{timestamp}.log", "detailed_progress": true}, "paths": {"assets_dir": "../assets", "results_dir": "../results", "cpp_kits_dir": "cpp_kits", "temp_dir": "../temp", "logs_dir": "../logs"}, "performance": {"memory_limit_mb": 8192, "parallel_processing": true, "resource_monitoring": true, "optimization_hints": true}}